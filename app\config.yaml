# CutRAG Assist 配置文件
# 切削參數查詢助理系統配置

# === 應用程式設置 ===
app:
  title: "切削參數助理"
  layout: "wide"
  max_turns: 10  # 最大對話輪次
  
  # 頁面顯示內容
  description: "**7變數切削參數查詢系統**：請提供材料、刀具形式、刀具直徑、切寬、品牌、刃數、切深等完整資訊來獲得精確的切削參數。"
  example: "📢 範例：「用10mm 4刃端銑刀切削鋁合金，切寬2mm，切深1mm，使用三菱品牌」"

# === LLM 設置 ===
llm:
  api_key: "empty"  # 本地LLM不需要API密鑰
  model: "gemma-2-9b"
  base_url: "http://localhost:8081/v1"  # 修改為8081避免與Streamlit衝突
  temperature: 0.3
  timeout: 30  # 秒

# === 資料庫設置 ===
database:
  path: "chat_history.db"
  # 連接池設置
  max_connections: 10
  connection_timeout: 30

# === 資料文件設置 ===
data:
  excel_path: "Data/cut_data.xlsx"
  
  # 支援的品牌列表
  supported_brands:
    - "MITSUBISHI MATERIALS"
    - "ISCAR" 
    - "SANDVIK"
    - "General"
  
  # Excel 工作表名稱
  sheets:
    mapping: "HomePage"
    brands:
      - "MITSUBISHI MATERIALS"
      - "ISCAR"
      - "SANDVIK"
      - "General"

# === 預設參數值 ===
defaults:
  cutting_parameters:
    tool_diam: 10  # mm
    cut_width_mm: 2.0  # mm
    flute_count: 4  # 刃數
    depth_of_cut: 1.0  # mm
    cutting_speed: 100  # m/min
    depth_factor: 2.0

  # 計算安全限制
  safety_limits:
    max_depth_ratio: 2.0  # 最大切深比例 (相對於直徑)
    min_tool_diameter: 0.5  # mm
    max_tool_diameter: 50  # mm
    min_cutting_speed: 10  # m/min
    max_cutting_speed: 1000  # m/min

# === 7變數參數配置 ===
parameters:
  required:
    - "part_material"
    - "tool_type"
    - "tool_diam"
    - "cut_width_mm"
    - "brand"
    - "flute_count" 
    - "depth_of_cut"
  
  # 參數驗證規則
  validation:
    tool_diam:
      min: 0.5
      max: 50
      unit: "mm"
    cut_width_mm:
      min: 0.1
      max: 100
      unit: "mm"
    flute_count:
      min: 1
      max: 12
      unit: "刃"
    depth_of_cut:
      min: 0.01
      max: 50
      unit: "mm"

# === 系統提示詞 ===
prompts:
  system: |
    你是一位專業的切削參數顧問。請根據用戶查詢和7變數計算結果，提供專業且友善的統整回答。

    **回答要求：**
    1. 先友善地確認用戶的查詢需求
    2. 列出結果使用的7個參數
    3. 重點突出最終計算結果（主軸轉速和進給速度）
    4. 如有警告訊息，要特別提醒用戶注意
    5. 不要重複用戶輸入的原始查詢內容

    **回答風格：**
    - 專業但親切，像資深技師在指導
    - 結構清晰，重點明確
    - 如果有安全注意事項要特別強調

  welcome: "您好！我是切削參數查詢助理（7變數模式）。請提供您的切削需求，包含完整的7個參數：材料、刀具形式、刀具直徑、切寬、品牌、刃數、切深。"
  
  request_query: "請提供您的查詢內容。"

# === 日誌設置 ===
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/cutrag.log"
  max_size: 10  # MB
  backup_count: 5
  
  # 模組級日誌控制
  loggers:
    llm_service: "INFO"
    database: "WARNING"
    data_processing: "INFO"
    ui: "WARNING"

# === 快取設置 ===
cache:
  enabled: true
  ttl: 3600  # 秒 (1小時)
  max_size: 100  # MB
  
  # 快取策略
  data_cache:
    excel_data: true
    material_mapping: true
    tool_mapping: true

# === UI 設置 ===
ui:
  theme:
    primary_color: "#FF6B6B"
    background_color: "#FFFFFF"
    text_color: "#333333"
  
  # 介面組件設置
  chat:
    max_messages: 50
    auto_scroll: true
    show_timestamps: false
  
  calculation_panel:
    auto_open: false
    position: "right"
    width: "400px"
  
  # UI 文字說明
  llm_description: "系統會自動使用LLM統整7變數計算結果，提供更自然的回答。"

# === 開發設置 ===
development:
  debug: false
  hot_reload: false
  profiling: false
  
  # 測試模式設置
  test_mode: false
  mock_llm: false
  sample_data: false 