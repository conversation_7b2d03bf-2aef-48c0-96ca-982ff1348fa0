"""
聊天服務模組
整合所有服務類，提供統一的聊天接口
"""

from .llm_service import LLMService
from .parameter_parser import ParameterParserService
from .cutting_service import CuttingParameterService
from .message_formatter import MessageFormatterService
from config import get_config


class CuttingAssistantService:
    """切削助理主服務類，整合所有功能模組"""

    def __init__(self, df, material_mapping, enhanced_material_mapping,
                 tool_type_mapping, enhanced_tool_mapping,
                 api_key=None, model=None, base_url=None):

        # 載入配置
        config = get_config()
        llm_config = config.llm_config

        # 使用配置文件中的預設值
        api_key = api_key or llm_config.get('api_key', 'empty')
        model = model or llm_config.get('model', 'gpt-4o-mini')
        base_url = base_url or llm_config.get(
            'base_url', 'http://localhost:8081/v1')

        # 初始化各個服務模組
        self.llm_service = LLMService(api_key, model, base_url)
        self.parser_service = ParameterParserService(
            material_mapping, enhanced_material_mapping,
            tool_type_mapping, enhanced_tool_mapping
        )
        self.cutting_service = CuttingParameterService(df)
        self.formatter_service = MessageFormatterService()

        # 保留向後兼容的屬性
        self.client = self.llm_service.client
        self.model = self.llm_service.model
        self.sys_prompt = self.llm_service.sys_prompt

    def query_cutting_parameters(self, user_query: str) -> tuple:
        """
        7變數查詢系統主入口

        Args:
            user_query: 用戶查詢字串

        Returns:
            (回復訊息, 原始查詢結果)
        """
        # Step 1: 解析用戶查詢
        parsed = self.parser_service.parse_user_query(user_query)

        # Step 2: 驗證參數完整性
        missing_params = self.parser_service.validate_parameters(parsed)

        # Step 3: 如果缺少參數，返回提示
        if missing_params:
            return self.formatter_service.format_missing_params_message(missing_params), None

        # Step 4: 提取查詢參數
        args = self.parser_service.extract_query_args(parsed)

        # Step 5: 執行切削參數查詢
        result = self.cutting_service.query_parameters(args)

        # Step 6: 格式化基本結果
        material_name = self.parser_service.get_material_name(
            args['part_material'])
        tool_name = self.parser_service.get_tool_name(args['tool_type'])
        formatted_result = self.cutting_service.format_basic_result(
            args, result, material_name, tool_name)

        # Step 7: 使用 LLM 生成自然語言回應
        llm_status = self.llm_service.get_connection_status()
        if llm_status['available']:
            llm_response, error = self.llm_service.generate_response(
                user_query, formatted_result)

            if llm_response:
                return llm_response, result
            else:
                # LLM 失敗時的後備回應
                return self.llm_service.get_fallback_response(formatted_result, error), result
        else:
            # LLM 不可用時直接返回格式化結果，並附加說明
            fallback_message = f"{formatted_result}\n\n*註：LLM統整功能不可用（{llm_status['message']}），已為您提供原始計算結果。*"
            return fallback_message, result

    def chat(self, messages):
        """
        主要聊天方法，使用7變數查詢系統

        Args:
            messages: 對話訊息列表

        Returns:
            (更新的訊息列表, 回復內容, 原始查詢結果)
        """
        # 檢查是否為系統消息開始的對話
        if len(messages) == 1 and messages[0]["role"] == "system":
            welcome_msg = self.formatter_service.format_welcome_message()
            return messages, welcome_msg, None

        # 獲取最後的用戶消息
        user_messages = [msg for msg in messages if msg["role"] == "user"]
        if not user_messages:
            request_msg = self.formatter_service.format_request_query_message()
            return messages, request_msg, None

        latest_user_message = user_messages[-1]["content"]

        # 使用7變數查詢系統
        reply, raw_result = self.query_cutting_parameters(latest_user_message)

        # 更新對話歷史
        updated_messages = messages.copy()
        updated_messages.append({"role": "assistant", "content": reply})

        return updated_messages, reply, raw_result

# 向後兼容：保留原始的簡化版服務類


class SimpleCuttingParameterService:
    """簡化版切削參數查詢服務，僅用於測試"""

    def __init__(self, df, material_mapping, enhanced_material_mapping, tool_type_mapping, enhanced_tool_mapping):
        # 使用新的模組化服務
        self.main_service = CuttingAssistantService(
            df, material_mapping, enhanced_material_mapping,
            tool_type_mapping, enhanced_tool_mapping
        )

    def query_cutting_parameters(self, user_query: str) -> tuple:
        """查詢切削參數"""
        reply, raw_result = self.main_service.query_cutting_parameters(
            user_query)

        if raw_result is None:
            return 'missing_params', reply
        elif isinstance(raw_result, dict) and 'error' in raw_result:
            return 'error', raw_result['error']
        else:
            return 'success', reply

    def chat(self, user_query: str) -> str:
        """簡化版聊天接口"""
        reply, _ = self.main_service.query_cutting_parameters(user_query)
        return reply
