"""
智能匹配模組
處理材料、刀具和品牌的智能匹配邏輯
"""


def smart_material_matching(query: str, material_mapping: dict, enhanced_material_mapping: dict) -> tuple:
    """
    智能材料匹配：先比對完整名稱，再比對簡稱。
    回傳 (material_code, material_name, confidence)
    """
    # ① 先比對完整名稱（長字串優先）
    for name, code in sorted(material_mapping.items(),
                             key=lambda kv: len(kv[0]), reverse=True):
        if name in query:
            return code, name, 'high'

    # ② 再比對簡稱（長字串優先）
    for name, code in sorted(enhanced_material_mapping.items(),
                             key=lambda kv: len(kv[0]), reverse=True):
        if name in query:
            full_name = next((k for k, v in material_mapping.items() if v == code),
                             name)
            return code, full_name, 'medium'

    return None, None, 'none'


def smart_tool_matching(query: str, tool_type_mapping: dict, enhanced_tool_mapping: dict) -> tuple:
    """
    智能刀具匹配：先比對完整名稱，再比對簡稱。
    回傳 (tool_code, tool_name, confidence)
    """
    for name, code in sorted(tool_type_mapping.items(),
                             key=lambda kv: len(kv[0]), reverse=True):
        if name in query:
            return code, name, 'high'

    for name, code in sorted(enhanced_tool_mapping.items(),
                             key=lambda kv: len(kv[0]), reverse=True):
        if name in query:
            full_name = next((k for k, v in tool_type_mapping.items() if v == code),
                             name)
            return code, full_name, 'medium'

    return None, None, 'none'


def smart_brand_matching(query: str) -> tuple:
    """
    智能品牌匹配：從查詢中識別品牌
    回傳 (brand_name, confidence)
    """
    brand_mapping = {
        'MITSUBISHI MATERIALS': ['mitsubishi', '三菱', '三菱材料'],
        'ISCAR': ['iscar', 'ic'],
        'SANDVIK': ['sandvik', 'sv', '山高'],
        'General': ['general', '通用', '一般', '其他', '其它']
    }

    query_lower = query.lower()

    # 按品牌優先級檢查，避免誤匹配
    for brand, keywords in brand_mapping.items():
        for keyword in keywords:
            if keyword in query_lower:
                return brand, 'high'

    # 如果沒有明確品牌，回傳General
    return 'General', 'medium'


def determine_brand_by_priority(user_brand: str) -> str:
    """
    根據品牌優先級決定查詢品牌
    如果不是 'MITSUBISHI MATERIALS', 'ISCAR', 'SANDVIK' 中的一個，則使用 'General'
    """
    valid_brands = ['MITSUBISHI MATERIALS', 'ISCAR', 'SANDVIK']

    if user_brand in valid_brands:
        return user_brand
    else:
        return 'General'
