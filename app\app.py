"""
切削參數查詢助理主應用程式
重構後的模組化版本 - 使用配置管理系統
"""

import os
import sys

# 設定環境編碼，解決 Windows cp950 編碼問題
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    sys.stderr.reconfigure(encoding='utf-8', errors='replace')

import streamlit as st
import json

# 導入配置管理
from config import get_config

# 導入模組化元件
from ui import (
    get_app_styles,
    initialize_session_state,
    render_sidebar,
    render_chat_history,
    render_user_input,
    render_calculation_panel
)
from utils.data_loader import get_database, load_cutting_data, initialize_service

# 載入配置
config = get_config()
app_config = config.app_config

# 設定頁面配置
st.set_page_config(
    page_title=app_config.get('title', '切削參數助理'),
    layout=app_config.get('layout', 'wide')
)

# 載入自定義 CSS 樣式
st.markdown(get_app_styles(), unsafe_allow_html=True)

# 顯示標題和說明
st.title(f"🔧 {app_config.get('title', '切削參數助理')}")
st.write(app_config.get('description', '7變數切削參數查詢系統'))
st.info(app_config.get('example', '範例：用10mm 4刃端銑刀切削鋁合金'))

# 初始化資料庫
db = get_database()

# 載入切削參數資料
df, material_mapping, enhanced_material_mapping, tool_type_mapping, enhanced_tool_mapping = load_cutting_data()

# 初始化服務
service, llm_base_url = initialize_service(
    df, material_mapping, enhanced_material_mapping,
    tool_type_mapping, enhanced_tool_mapping
)

# 初始化 session state
initialize_session_state(db, config)

# 渲染側邊欄
render_sidebar(service, db, llm_base_url)

# --- 主要內容區 ---
# 獲取當前對話的訊息
current_conversation_id = st.session_state.current_conversation_id
messages = db.get_conversation_messages(current_conversation_id)

# 如果是新對話，添加系統提示
if not messages:
    db.add_message(current_conversation_id, "system", service.sys_prompt)
    messages = [{"role": "system", "content": service.sys_prompt}]

# 獲取該對話的所有檢索結果
lookup_results = db.get_conversation_lookups(current_conversation_id)

# 渲染對話歷史
render_chat_history(messages, lookup_results, current_conversation_id)

# 渲染用戶輸入區域
render_user_input(service, db, current_conversation_id)

# 渲染浮動計算詳情面板
render_calculation_panel(db)
