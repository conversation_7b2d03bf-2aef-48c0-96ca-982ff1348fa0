"""
參數解析服務模組
負責自然語言參數解析和驗證
"""

from data import parse_natural_language
from config import get_config


class ParameterParserService:
    """參數解析服務類"""

    def __init__(self, material_mapping, enhanced_material_mapping, tool_type_mapping, enhanced_tool_mapping):
        self.material_mapping = material_mapping
        self.enhanced_material_mapping = enhanced_material_mapping
        self.tool_type_mapping = tool_type_mapping
        self.enhanced_tool_mapping = enhanced_tool_mapping

        # 從配置文件讀取必需的參數列表
        config = get_config()
        self.required_params = config.parameters_config.get('required', [
            'part_material', 'tool_type', 'tool_diam',
            'cut_width_mm', 'brand', 'flute_count', 'depth_of_cut'
        ])

    def parse_user_query(self, user_query: str):
        """解析用戶查詢，提取7個參數"""
        return parse_natural_language(
            user_query,
            self.material_mapping,
            self.enhanced_material_mapping,
            self.tool_type_mapping,
            self.enhanced_tool_mapping
        )

    def validate_parameters(self, parsed_params):
        """驗證參數完整性，返回缺失的參數列表"""
        missing_params = []

        for param in self.required_params:
            if not parsed_params['confidence'][param] or parsed_params[param] is None:
                missing_params.append(param)

        return missing_params

    def extract_query_args(self, parsed_params):
        """從解析結果中提取查詢參數"""
        return {
            'part_material': parsed_params['part_material'],
            'tool_type': parsed_params['tool_type'],
            'brand': parsed_params['brand'],
            'tool_diam': parsed_params['tool_diam'],
            'cut_width_mm': parsed_params['cut_width_mm'],
            'flute_count': parsed_params['flute_count'],
            'depth_of_cut': parsed_params['depth_of_cut']
        }

    def get_material_name(self, material_code):
        """根據材料代碼獲取材料名稱"""
        for name, code in self.enhanced_material_mapping.items():
            if code == material_code:
                return name
        return f"代碼{material_code}"

    def get_tool_name(self, tool_code):
        """根據刀具代碼獲取刀具名稱"""
        for name, code in self.enhanced_tool_mapping.items():
            if code == tool_code:
                return name
        return f"代碼{tool_code}"
