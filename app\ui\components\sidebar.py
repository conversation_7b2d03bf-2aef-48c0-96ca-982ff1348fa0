"""
側邊欄元件
負責對話管理、LLM 狀態顯示和對話歷史
"""

import streamlit as st
import time
from ..state_management import reset_conversation_state
from config import get_config


def toggle_conversation_menu(conv_id):
    """切換顯示對話選單"""
    if st.session_state.show_conversation_menu == conv_id:
        st.session_state.show_conversation_menu = None
    else:
        st.session_state.show_conversation_menu = conv_id


def render_sidebar(service, db, llm_base_url):
    """渲染側邊欄"""
    with st.sidebar:
        # 使用配置文件中的標題
        config = get_config()
        app_title = config.app_config.get('title', '切削參數助理')
        st.title(f"🔧 {app_title}")

        # LLM 狀態顯示
        _render_llm_status(service, llm_base_url)

        st.divider()

        # 新增對話按鈕
        _render_new_conversation_button(db)

        st.divider()

        # 顯示所有對話
        _render_conversation_list(db)


def _render_llm_status(service, llm_base_url):
    """渲染 LLM 狀態"""
    # 初始化快取變數
    if "llm_status_cache" not in st.session_state:
        st.session_state.llm_status_cache = None
        st.session_state.llm_status_timestamp = 0

    # 檢查是否需要刷新狀態（30秒快取）
    current_time = time.time()
    cache_duration = 300  # 30秒快取

    if (st.session_state.llm_status_cache is None or
            current_time - st.session_state.llm_status_timestamp > cache_duration):

        # 更新快取
        if hasattr(service, 'llm_service'):
            st.session_state.llm_status_cache = service.llm_service.get_connection_status()
        else:
            st.session_state.llm_status_cache = {
                "available": False, "message": "LLM 服務未正確初始化"}

        st.session_state.llm_status_timestamp = current_time

    # 顯示狀態
    status = st.session_state.llm_status_cache
    if status and status['available']:
        st.success("🤖 LLM統整功能：已啟用")
    else:
        st.warning("⚠️ LLM統整功能：未啟用")

    # LLM配置展開區域
    with st.expander("⚙️ LLM配置", expanded=False):
        # 基本信息
        col1, col2 = st.columns([3, 1])
        with col1:
            st.text(f"模型: {service.model}")
            st.text(f"端點: {llm_base_url}")
        with col2:
            # 刷新按鈕
            if st.button("🔄", help="刷新狀態", key="refresh_llm_status"):
                # 清除快取，強制刷新
                st.session_state.llm_status_cache = None
                st.rerun()

        # 顯示詳細的連接狀態
        if status:
            if status['available']:
                st.success(f"✅ 連線正常 - {status['message']}")
            else:
                st.error(f"❌ 連線失敗 - {status['message']}")

        # 快取時間提示
        if st.session_state.llm_status_timestamp > 0:
            cache_age = int(
                current_time - st.session_state.llm_status_timestamp)
            st.caption(f"狀態快取時間：{cache_age}秒前")

        # 從配置讀取說明文字
        config = get_config()
        llm_description = config.ui_config.get('llm_description',
                                               "系統會自動使用LLM統整計算結果，提供更自然的回答。")
        st.caption(llm_description)


def _render_new_conversation_button(db):
    """渲染新對話按鈕"""
    if st.button("➕ 新對話", use_container_width=True):
        new_id = db.create_conversation()
        st.session_state.current_conversation_id = new_id
        reset_conversation_state()
        st.rerun()


def _render_conversation_list(db):
    """渲染對話清單"""
    conversations = db.get_all_conversations()

    for conv in conversations:
        is_current = conv["id"] == st.session_state.current_conversation_id

        # 檢查是否正在編輯此對話標題
        if (hasattr(st.session_state, 'editing_conversation_id') and
                st.session_state.editing_conversation_id == conv["id"]):
            _render_conversation_edit_mode(conv, db)
        else:
            _render_conversation_normal_mode(conv, is_current, db)

        # 顯示對話選單
        if st.session_state.show_conversation_menu == conv["id"]:
            _render_conversation_menu(conv)

        # 顯示刪除確認
        if f"confirm_delete_{conv['id']}" in st.session_state:
            _render_delete_confirmation(conv, db)

        # 添加間距
        st.markdown("<br>", unsafe_allow_html=True)


def _render_conversation_edit_mode(conv, db):
    """渲染對話編輯模式"""
    with st.form(key=f"edit_title_form_{conv['id']}"):
        new_title = st.text_input(
            "",
            value=conv["title"],
            key=f"edit_input_{conv['id']}",
            label_visibility="collapsed"
        )
        col1, col2 = st.columns(2)
        with col1:
            if st.form_submit_button("保存", use_container_width=True, help="保存"):
                if new_title.strip():
                    db.update_conversation_title(conv["id"], new_title.strip())
                del st.session_state.editing_conversation_id
                st.rerun()
        with col2:
            if st.form_submit_button("取消", use_container_width=True, help="取消"):
                del st.session_state.editing_conversation_id
                st.rerun()


def _render_conversation_normal_mode(conv, is_current, db):
    """渲染對話正常顯示模式"""
    col1, col2 = st.columns([5, 1])

    with col1:
        # 主要對話按鈕
        button_label = f"💬 {conv['title']}" + (" ●" if is_current else "")
        if st.button(
            button_label,
            key=f"conv_{conv['id']}",
            use_container_width=True,
            type="secondary"
        ):
            if conv["id"] != st.session_state.current_conversation_id:
                st.session_state.current_conversation_id = conv["id"]
                st.session_state.input_text = ""  # 清空輸入框
                st.session_state.form_key += 1  # 強制重新渲染表單
                st.rerun()

    with col2:
        # "..." 菜單按鈕
        if st.button("⋮", key=f"menu_{conv['id']}", help="選項", use_container_width=True):
            toggle_conversation_menu(conv["id"])
            st.rerun()


def _render_conversation_menu(conv):
    """渲染對話選單"""
    col_menu1, col_menu2 = st.columns(2)
    with col_menu1:
        if st.button("✏️ 重新命名", key=f"rename_{conv['id']}", use_container_width=True):
            st.session_state.editing_conversation_id = conv["id"]
            st.session_state.show_conversation_menu = None
            st.rerun()
    with col_menu2:
        if st.button("🗑️ 刪除", key=f"delete_{conv['id']}", use_container_width=True):
            st.session_state[f"confirm_delete_{conv['id']}"] = True
            st.session_state.show_conversation_menu = None
            st.rerun()


def _render_delete_confirmation(conv, db):
    """渲染刪除確認對話框"""
    st.warning(f"⚠️ 確定要刪除對話「**{conv['title']}**」嗎？此操作無法復原")
    col_yes, col_no = st.columns(2)

    with col_yes:
        if st.button("確認刪除", key=f"confirm_yes_{conv['id']}",
                     type="primary", use_container_width=True):
            _handle_conversation_deletion(conv["id"], db)

    with col_no:
        if st.button("取消", key=f"confirm_no_{conv['id']}", use_container_width=True):
            del st.session_state[f"confirm_delete_{conv['id']}"]
            st.rerun()


def _handle_conversation_deletion(conv_id, db):
    """處理對話刪除"""
    db.delete_conversation(conv_id)

    # 獲取剩餘對話
    remaining_conversations = db.get_all_conversations()
    if remaining_conversations:
        st.session_state.current_conversation_id = remaining_conversations[0]["id"]
    else:
        st.session_state.current_conversation_id = db.create_conversation()

    reset_conversation_state()
    del st.session_state[f"confirm_delete_{conv_id}"]
    st.rerun()
