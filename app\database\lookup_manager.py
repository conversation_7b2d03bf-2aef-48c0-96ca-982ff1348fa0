"""
檢索結果管理模組  
處理切削參數檢索結果的儲存和查詢操作
"""

from datetime import datetime
from .connection import DatabaseConnection


class LookupManager:
    """檢索結果管理器"""

    def __init__(self, db_connection: DatabaseConnection):
        self.db = db_connection

    def add_lookup_result(self, conversation_id, message_id, data):
        """儲存檢索結果，返回檢索結果 ID"""
        query = "INSERT INTO lookup_results (conversation_id, message_id, data) VALUES (?, ?, ?)"
        params = (conversation_id, message_id, data)

        lookup_id = self.db.execute_insert(query, params)
        return lookup_id

    def get_conversation_lookups(self, conversation_id):
        """取得指定對話的所有檢索結果"""
        query = "SELECT id, message_id, data, created_at FROM lookup_results WHERE conversation_id = ? ORDER BY created_at"
        params = (conversation_id,)

        results = self.db.execute_query(query, params)
        lookups = [
            {
                "id": row[0],
                "message_id": row[1],
                "data": row[2],
                "created_at": row[3]
            }
            for row in results
        ]

        return lookups

    def get_lookup_by_id(self, lookup_id):
        """根據 ID 取得特定的檢索結果"""
        query = "SELECT id, conversation_id, message_id, data, created_at FROM lookup_results WHERE id = ?"
        params = (lookup_id,)

        results = self.db.execute_query(query, params)
        if results:
            row = results[0]
            return {
                "id": row[0],
                "conversation_id": row[1],
                "message_id": row[2],
                "data": row[3],
                "created_at": row[4]
            }
        return None

    def get_message_lookups(self, message_id):
        """取得特定訊息的所有檢索結果"""
        query = "SELECT id, conversation_id, data, created_at FROM lookup_results WHERE message_id = ? ORDER BY created_at"
        params = (message_id,)

        results = self.db.execute_query(query, params)
        lookups = [
            {
                "id": row[0],
                "conversation_id": row[1],
                "data": row[2],
                "created_at": row[3]
            }
            for row in results
        ]

        return lookups

    def update_lookup_data(self, lookup_id, new_data):
        """更新檢索結果資料"""
        query = "UPDATE lookup_results SET data = ? WHERE id = ?"
        params = (new_data, lookup_id)

        return self.db.execute_update(query, params)

    def delete_lookup(self, lookup_id):
        """刪除特定檢索結果"""
        query = "DELETE FROM lookup_results WHERE id = ?"
        params = (lookup_id,)

        return self.db.execute_update(query, params)

    def delete_message_lookups(self, message_id):
        """刪除特定訊息的所有檢索結果"""
        query = "DELETE FROM lookup_results WHERE message_id = ?"
        params = (message_id,)

        return self.db.execute_update(query, params)

    def get_latest_lookups(self, conversation_id, limit=10):
        """取得指定對話的最新 N 個檢索結果"""
        query = "SELECT id, message_id, data, created_at FROM lookup_results WHERE conversation_id = ? ORDER BY created_at DESC LIMIT ?"
        params = (conversation_id, limit)

        results = self.db.execute_query(query, params)
        lookups = [
            {
                "id": row[0],
                "message_id": row[1],
                "data": row[2],
                "created_at": row[3]
            }
            for row in results
        ]

        return lookups

    def lookup_exists(self, lookup_id):
        """檢查檢索結果是否存在"""
        query = "SELECT COUNT(*) FROM lookup_results WHERE id = ?"
        params = (lookup_id,)

        results = self.db.execute_query(query, params)
        return results[0][0] > 0 if results else False
