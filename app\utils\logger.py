"""
日誌系統模組
提供統一的日誌管理功能，替代 print() 語句
"""

import logging
import os
from pathlib import Path
from logging.handlers import RotatingFileHandler
from typing import Optional
from config import get_config


class LoggerManager:
    """日誌管理器"""

    def __init__(self):
        self._loggers = {}
        self._config = get_config().logging_config
        self._setup_root_logger()

    def _setup_root_logger(self):
        """設置根日誌器"""
        # 創建日誌目錄
        log_file = self._config.get('file', 'logs/cutrag.log')
        log_dir = Path(log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)

        # 設置根日誌級別
        level = getattr(logging, self._config.get('level', 'INFO').upper())
        logging.basicConfig(level=level)

        # 設置格式
        formatter = logging.Formatter(
            self._config.get(
                'format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )

        # 設置文件處理器（輪轉日誌）
        max_size = self._config.get(
            'max_size', 10) * 1024 * 1024  # MB to bytes
        backup_count = self._config.get('backup_count', 5)

        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=max_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(level)

        # 設置控制台處理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(level)

        # 為根日誌器添加處理器
        root_logger = logging.getLogger()
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)

    def get_logger(self, name: str) -> logging.Logger:
        """
        獲取指定名稱的日誌器

        Args:
            name: 日誌器名稱

        Returns:
            日誌器實例
        """
        if name not in self._loggers:
            logger = logging.getLogger(name)

            # 從配置設置特定模組的日誌級別
            loggers_config = self._config.get('loggers', {})
            if name in loggers_config:
                level_name = loggers_config[name].upper()
                level = getattr(logging, level_name, logging.INFO)
                logger.setLevel(level)

            self._loggers[name] = logger

        return self._loggers[name]


# 全域日誌管理器實例
_logger_manager: Optional[LoggerManager] = None


def get_logger(name: str) -> logging.Logger:
    """
    獲取日誌器實例

    Args:
        name: 日誌器名稱（通常使用模組名稱）

    Returns:
        日誌器實例
    """
    global _logger_manager
    if _logger_manager is None:
        _logger_manager = LoggerManager()

    return _logger_manager.get_logger(name)


# 便利函數
def get_app_logger() -> logging.Logger:
    """獲取應用程式主日誌器"""
    return get_logger('app')


def get_llm_logger() -> logging.Logger:
    """獲取 LLM 服務日誌器"""
    return get_logger('llm_service')


def get_database_logger() -> logging.Logger:
    """獲取資料庫日誌器"""
    return get_logger('database')


def get_data_logger() -> logging.Logger:
    """獲取資料處理日誌器"""
    return get_logger('data_processing')


def get_ui_logger() -> logging.Logger:
    """獲取 UI 日誌器"""
    return get_logger('ui')


# 裝飾器：為函數添加日誌記錄
def log_function_call(logger: Optional[logging.Logger] = None):
    """
    裝飾器：記錄函數調用

    Args:
        logger: 使用的日誌器，預設為 app 日誌器
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            log = logger or get_app_logger()
            log.debug(
                f"調用函數: {func.__name__} with args={args}, kwargs={kwargs}")

            try:
                result = func(*args, **kwargs)
                log.debug(f"函數 {func.__name__} 執行成功")
                return result
            except Exception as e:
                log.error(f"函數 {func.__name__} 執行失敗: {e}")
                raise

        return wrapper
    return decorator


# 上下文管理器：記錄操作時間
class LogTimer:
    """日誌計時器上下文管理器"""

    def __init__(self, operation_name: str, logger: Optional[logging.Logger] = None):
        self.operation_name = operation_name
        self.logger = logger or get_app_logger()
        self.start_time = None

    def __enter__(self):
        import time
        self.start_time = time.time()
        self.logger.info(f"開始 {self.operation_name}")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        import time
        elapsed = time.time() - self.start_time

        if exc_type is None:
            self.logger.info(f"完成 {self.operation_name} (耗時: {elapsed:.2f}秒)")
        else:
            self.logger.error(
                f"失敗 {self.operation_name} (耗時: {elapsed:.2f}秒): {exc_val}")


__all__ = [
    'get_logger',
    'get_app_logger',
    'get_llm_logger',
    'get_database_logger',
    'get_data_logger',
    'get_ui_logger',
    'log_function_call',
    'LogTimer'
]
