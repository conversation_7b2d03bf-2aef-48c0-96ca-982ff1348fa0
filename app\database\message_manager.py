"""
訊息管理模組
處理聊天訊息的新增、查詢和管理操作
"""

from datetime import datetime
from .connection import DatabaseConnection


class MessageManager:
    """訊息管理器"""

    def __init__(self, db_connection: DatabaseConnection):
        self.db = db_connection

    def add_message(self, conversation_id, role, content):
        """新增訊息到指定對話，返回訊息 ID"""
        query = "INSERT INTO messages (conversation_id, role, content) VALUES (?, ?, ?)"
        params = (conversation_id, role, content)

        message_id = self.db.execute_insert(query, params)
        return message_id

    def get_conversation_messages(self, conversation_id):
        """取得指定對話的所有訊息"""
        query = "SELECT id, role, content, created_at FROM messages WHERE conversation_id = ? ORDER BY created_at"
        params = (conversation_id,)

        results = self.db.execute_query(query, params)
        messages = [
            {
                "id": row[0],
                "role": row[1],
                "content": row[2],
                "created_at": row[3]
            }
            for row in results
        ]

        return messages

    def get_message_by_id(self, message_id):
        """根據 ID 取得特定訊息"""
        query = "SELECT id, conversation_id, role, content, created_at FROM messages WHERE id = ?"
        params = (message_id,)

        results = self.db.execute_query(query, params)
        if results:
            row = results[0]
            return {
                "id": row[0],
                "conversation_id": row[1],
                "role": row[2],
                "content": row[3],
                "created_at": row[4]
            }
        return None

    def update_message_content(self, message_id, new_content):
        """更新訊息內容"""
        query = "UPDATE messages SET content = ? WHERE id = ?"
        params = (new_content, message_id)

        return self.db.execute_update(query, params)

    def delete_message(self, message_id):
        """刪除特定訊息"""
        query = "DELETE FROM messages WHERE id = ?"
        params = (message_id,)

        return self.db.execute_update(query, params)

    def get_messages_by_role(self, conversation_id, role):
        """取得指定對話中特定角色的所有訊息"""
        query = "SELECT id, role, content, created_at FROM messages WHERE conversation_id = ? AND role = ? ORDER BY created_at"
        params = (conversation_id, role)

        results = self.db.execute_query(query, params)
        messages = [
            {
                "id": row[0],
                "role": row[1],
                "content": row[2],
                "created_at": row[3]
            }
            for row in results
        ]

        return messages

    def get_latest_messages(self, conversation_id, limit=10):
        """取得指定對話的最新 N 條訊息"""
        query = "SELECT id, role, content, created_at FROM messages WHERE conversation_id = ? ORDER BY created_at DESC LIMIT ?"
        params = (conversation_id, limit)

        results = self.db.execute_query(query, params)
        messages = [
            {
                "id": row[0],
                "role": row[1],
                "content": row[2],
                "created_at": row[3]
            }
            for row in results
        ]

        # 回復正確的時間順序
        return list(reversed(messages))

    def message_exists(self, message_id):
        """檢查訊息是否存在"""
        query = "SELECT COUNT(*) FROM messages WHERE id = ?"
        params = (message_id,)

        results = self.db.execute_query(query, params)
        return results[0][0] > 0 if results else False
