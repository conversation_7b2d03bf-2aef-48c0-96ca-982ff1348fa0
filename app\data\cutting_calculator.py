"""
切削參數計算模組
處理切削參數查詢和計算功能
"""

import math
import pandas as pd
from .smart_matchers import determine_brand_by_priority


def lookup_cutting_param(df: pd.DataFrame, part_material: int, tool_type: int,
                         brand: str, **kwargs) -> dict:
    """
    7變數切削參數查詢函數
    根據材料 + 刀具形式 + 品牌查詢基礎參數，然後進行運算

    Args:
        df: 切削參數資料框
        part_material: 材料代碼
        tool_type: 刀具型式代碼  
        brand: 品牌名稱
        **kwargs: 其他參數（tool_diam, cut_width_mm, flute_count, depth_of_cut 等）

    Returns:
        dict: 查詢結果包含計算後的 S_rpm 和 Feed_mm_min
    """
    try:
        # 確定使用的品牌
        search_brand = determine_brand_by_priority(brand)

        # 根據材料 + 刀具型式 + 品牌查詢基礎參數
        filtered_df = df[
            (df['part_material'] == part_material) &
            (df['tool_type'] == tool_type) &
            (df['Brand'] == search_brand)
        ]

        if filtered_df.empty:
            return {"error": f"查無符合條件的切削參數：材料代碼{part_material}，刀具型式{tool_type}，品牌{search_brand}"}

        # 取第一筆作為基礎參數（之後可以加入更精確的選擇邏輯）
        base_params = filtered_df.iloc[0]

        # 從 kwargs 中獲取參數用於運算
        calculation_params = _extract_calculation_params(kwargs)

        # 獲取計算因子
        calculation_factors = _extract_calculation_factors(base_params)

        # 執行切削參數計算
        result = _calculate_cutting_parameters(
            calculation_params, calculation_factors, search_brand)

        return result

    except Exception as e:
        return {"error": f"查詢失敗：{str(e)}"}


def _extract_calculation_params(kwargs: dict) -> dict:
    """從 kwargs 中提取計算參數"""
    return {
        'tool_diam': kwargs.get('tool_diam', 10),  # 預設10mm (D)
        'cut_width_mm': kwargs.get('cut_width_mm', 2.0),  # 預設2mm
        'flute_count': kwargs.get('flute_count', 4),  # 預設4刃
        'depth_of_cut': kwargs.get('depth_of_cut', 1.0),  # 預設1mm
    }


def _extract_calculation_factors(base_params: pd.Series) -> dict:
    """從基礎參數中提取計算因子"""
    factors = {
        'depth_factor': base_params.get('depth_factor', 2.0),  # 切深因子
        # Vcstd (m/min)
        'cutting_speed_std': base_params.get('cutting_speed', 100),
        'X4': base_params.get('X4', 0),
        'X3': base_params.get('X3', 0),
        'X2': base_params.get('X2', 0),
        'X': base_params.get('X', 0),
        'C': base_params.get('C', 0),
    }

    # 轉換為數值型別，處理 NaN
    for key, value in factors.items():
        factors[key] = float(value) if pd.notna(value) else (100 if key == 'cutting_speed_std' else
                                                             2.0 if key == 'depth_factor' else 0)

    return factors


def _calculate_cutting_parameters(calc_params: dict, calc_factors: dict, search_brand: str) -> dict:
    """執行完整的切削參數計算"""
    D = float(calc_params['tool_diam'])  # 刀具直徑
    cut_width_mm = float(calc_params['cut_width_mm'])
    flute_count = float(calc_params['flute_count'])
    depth_of_cut = calc_params['depth_of_cut']

    # 步驟 1: 獲取計算因子
    X4, X3, X2, X, C = calc_factors['X4'], calc_factors['X3'], calc_factors['X2'], calc_factors['X'], calc_factors['C']
    cutting_speed_std = calc_factors['cutting_speed_std']
    depth_factor = calc_factors['depth_factor']

    # 步驟 2: 每刃進刀量(Ftstd) = X4*D^4+X3*D^3+X2*D^2+X*D+C
    Ftstd = X4 * (D**4) + X3 * (D**3) + X2 * (D**2) + X * D + C

    # 步驟 3: 切深檢查
    Apstd = 2 * D
    depth_warning = None
    if depth_of_cut > Apstd:
        depth_warning = f"⚠️ 警告：切深 {depth_of_cut}mm 超過建議最大值 {Apstd:.1f}mm"

    # 步驟 4: 切削角度計算
    cutting_angle_results = _calculate_cutting_angles(cut_width_mm, D)
    Cw, Cdeg = cutting_angle_results['Cw'], cutting_angle_results['Cdeg']

    # 步驟 5: 進給計算
    feed_results = _calculate_feed_parameters(
        Ftstd, Cdeg, cutting_speed_std, Cw)
    Ft, Vc = feed_results['Ft'], feed_results['Vc']

    # 步驟 6: 最終計算
    final_results = _calculate_final_parameters(Vc, D, Ft, flute_count)
    N_rpm, F_mm_min = final_results['N_rpm'], final_results['F_mm_min']

    # 準備返回結果
    result = {
        "s_rpm": int(round(N_rpm)),
        "feed_mm_min": round(F_mm_min),
        "brand_used": search_brand,
        "calculation_details": {
            "Ftstd": round(Ftstd, 6),
            "Apstd": round(Apstd, 1),
            "Cw": round(Cw, 3),
            "Cdeg": round(Cdeg, 1),
            "Ft": round(Ft, 6),
            "Vc": round(Vc, 1),
            "cutting_speed_std": cutting_speed_std,
            "depth_factor": depth_factor
        },
        "calculation_note": "使用完整切削力學公式計算"
    }

    # 如果有切深警告，加入結果
    if depth_warning:
        result["depth_warning"] = depth_warning

    return result


def _calculate_cutting_angles(cut_width_mm: float, D: float) -> dict:
    """計算切削角度相關參數"""
    # 步驟 4: Cw = 切寬 / 刀具直徑
    Cw = cut_width_mm / D

    # 步驟 5: Cdeg = cos-1(1 - 2 × Cw) 最大90，超過取90
    cos_value = 1 - 2 * Cw
    # 確保 cos_value 在有效範圍內 [-1, 1]
    cos_value = max(-1, min(1, cos_value))
    Cdeg_rad = math.acos(cos_value)
    Cdeg = math.degrees(Cdeg_rad)
    Cdeg = min(Cdeg, 90)  # 最大90度

    return {'Cw': Cw, 'Cdeg': Cdeg}


def _calculate_feed_parameters(Ftstd: float, Cdeg: float, cutting_speed_std: float, Cw: float) -> dict:
    """計算進給相關參數"""
    # 步驟 6: Ft = Ftstd / sin(Cdeg)
    Cdeg_rad_limited = math.radians(Cdeg)
    sin_Cdeg = math.sin(Cdeg_rad_limited)
    if sin_Cdeg == 0:
        sin_Cdeg = 0.01  # 避免除以零
    Ft = Ftstd / sin_Cdeg

    # 步驟 7: Vc = Vcstd/1.05 × (1.05 - 0.19 × ln(Cw))
    if Cw <= 0:
        Cw = 0.01  # 避免 ln(0)
    Vc = (cutting_speed_std / 1.05) * (1.05 - 0.19 * math.log(Cw))

    return {'Ft': Ft, 'Vc': Vc}


def _calculate_final_parameters(Vc: float, D: float, Ft: float, flute_count: float) -> dict:
    """計算最終的轉速和進給速度"""
    # 步驟 8: 主軸轉速 N(rpm) = Vc(m/min) / pi / D(mm) × 1000
    N_rpm = (Vc * 1000) / (math.pi * D)

    # 步驟 9: 進給速度 F(mm/min) = Ft(mm/tooth) × N(rpm) × 刃數
    F_mm_min = Ft * N_rpm * flute_count

    return {'N_rpm': N_rpm, 'F_mm_min': F_mm_min}
