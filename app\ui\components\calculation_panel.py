"""
計算詳情面板元件
顯示切削參數計算過程的浮動面板
"""

import streamlit as st
import json


def toggle_calculation_panel(lookup_id=None):
    """切換浮動面板顯示狀態"""
    # 如果當前面板已開啟且是同一個檢索結果，則關閉面板
    if (st.session_state.show_calculation_panel and
            st.session_state.selected_lookup_id == lookup_id):
        st.session_state.show_calculation_panel = False
        st.session_state.selected_lookup_id = None
    else:
        # 否則開啟面板並設定檢索結果
        st.session_state.show_calculation_panel = True
        if lookup_id is not None:
            st.session_state.selected_lookup_id = lookup_id


def close_calculation_panel():
    """關閉浮動面板"""
    st.session_state.show_calculation_panel = False
    st.session_state.selected_lookup_id = None


def render_calculation_panel(db):
    """渲染計算詳情面板"""
    if not st.session_state.show_calculation_panel:
        return

    # 獲取選定的檢索結果
    selected_result = None
    if st.session_state.selected_lookup_id:
        selected_lookup = db.get_lookup_by_id(
            st.session_state.selected_lookup_id)
        if selected_lookup:
            selected_result = json.loads(selected_lookup["data"])
    else:
        selected_result = st.session_state.raw_lookup_result

    # 建構完全的 HTML 浮動面板
    panel_html = ""
    if selected_result:
        if 'calculation_details' in selected_result:
            details = selected_result['calculation_details']

            # 建構步驟內容
            steps_html = _build_calculation_steps_html(
                details, selected_result)

            panel_html = f"""
            <div class="true-floating-panel">
                <div class="panel-header">
                    <h3 style="margin: 0;">🔬 計算步驟詳情</h3>
                </div>
                <div class="panel-content">
                    {steps_html}
                </div>
            </div>
            """
        elif "error" in selected_result:
            panel_html = f"""
            <div class="true-floating-panel">
                <div class="panel-header">
                    <h3 style="margin: 0;">❌ 查詢錯誤</h3>
                </div>
                <div class="panel-content">
                    <p style="color: red; font-size: 16px;"><strong>錯誤：</strong> {selected_result['error']}</p>
                </div>
            </div>
            """
        else:
            panel_html = f"""
            <div class="true-floating-panel">
                <div class="panel-header">
                    <h3 style="margin: 0;">ℹ️ 查詢結果</h3>
                </div>
                <div class="panel-content">
                    <p>此查詢結果無詳細計算過程資料</p>
                </div>
            </div>
            """

    # 顯示浮動面板
    st.markdown(panel_html, unsafe_allow_html=True)


def _build_calculation_steps_html(details, selected_result):
    """建構計算步驟的 HTML 內容"""
    steps_html = f"""
    <div style="margin: 15px 0;">
        <h4>📋 基礎查詢參數</h4>
        <p><strong>線速度標準值 Vcstd：</strong> {details.get('cutting_speed_std', 0):.1f} m/min</p>
        <p><strong>切深因子：</strong> {details.get('depth_factor', 0):.1f}</p>
        <hr style="margin: 15px 0;">
        <h4>🧮 計算過程</h4>
    </div>
    
    <details style="margin: 10px 0; border: 1px solid #e0e0e0; border-radius: 5px;">
        <summary style="cursor: pointer; font-weight: bold; padding: 10px; background: #f8f9fa; border-radius: 5px;">
            <strong>步驟2：每刃進刀量計算</strong>
        </summary>
        <div style="padding: 15px;">
            <div style="background: #f5f5f5; padding: 10px; border-radius: 3px; font-family: monospace; margin: 10px 0;">
                F<sub>tstd</sub> = X4×D⁴ + X3×D³ + X2×D² + X×D + C
            </div>
            <p><strong>結果：</strong> {details.get('Ftstd', 0):.6f} mm/tooth</p>
        </div>
    </details>
    
    <details style="margin: 10px 0; border: 1px solid #e0e0e0; border-radius: 5px;">
        <summary style="cursor: pointer; font-weight: bold; padding: 10px; background: #f8f9fa; border-radius: 5px;">
            <strong>步驟3-4：幾何參數</strong>
        </summary>
        <div style="padding: 15px;">
            <p><strong>切寬比 Cw：</strong> {details.get('Cw', 0):.3f}</p>
            <p><strong>建議最大切深 Apstd：</strong> {details.get('Apstd', 0):.1f} mm</p>
        </div>
    </details>
    
    <details style="margin: 10px 0; border: 1px solid #e0e0e0; border-radius: 5px;">
        <summary style="cursor: pointer; font-weight: bold; padding: 10px; background: #f8f9fa; border-radius: 5px;">
            <strong>步驟5-6：角度修正</strong>
        </summary>
        <div style="padding: 15px;">
            <div style="background: #f5f5f5; padding: 10px; border-radius: 3px; font-family: monospace; margin: 10px 0;">
                C<sub>deg</sub> = cos⁻¹(1 - 2×Cw)
            </div>
            <p><strong>角度 Cdeg：</strong> {details.get('Cdeg', 0):.1f}°</p>
            <div style="background: #f5f5f5; padding: 10px; border-radius: 3px; font-family: monospace; margin: 10px 0;">
                F<sub>t</sub> = F<sub>tstd</sub> / sin(C<sub>deg</sub>)
            </div>
            <p><strong>修正每刃進刀量 Ft：</strong> {details.get('Ft', 0):.6f} mm/tooth</p>
        </div>
    </details>
    
    <details style="margin: 10px 0; border: 1px solid #e0e0e0; border-radius: 5px;">
        <summary style="cursor: pointer; font-weight: bold; padding: 10px; background: #f8f9fa; border-radius: 5px;">
            <strong>步驟7：線速度修正</strong>
        </summary>
        <div style="padding: 15px;">
            <div style="background: #f5f5f5; padding: 10px; border-radius: 3px; font-family: monospace; margin: 10px 0;">
                V<sub>c</sub> = V<sub>cstd</sub>/1.05 × (1.05 - 0.19×ln(Cw))
            </div>
            <p><strong>修正線速度 Vc：</strong> {details.get('Vc', 0):.1f} m/min</p>
        </div>
    </details>
    
    <details style="margin: 10px 0; border: 1px solid #e0e0e0; border-radius: 5px;">
        <summary style="cursor: pointer; font-weight: bold; padding: 10px; background: #f8f9fa; border-radius: 5px;">
            <strong>步驟8-9：最終結果</strong>
        </summary>
        <div style="padding: 15px; background: #f0f8ff;">
            <div style="background: #e9ecef; padding: 10px; border-radius: 3px; font-family: monospace; margin: 10px 0;">
                N = V<sub>c</sub>×1000 / (π×D)
            </div>
            <p><strong>主軸轉速：</strong> {selected_result.get('s_rpm', 0)} rpm</p>
            <div style="background: #e9ecef; padding: 10px; border-radius: 3px; font-family: monospace; margin: 10px 0;">
                F = F<sub>t</sub>×N×刃數
            </div>
            <p><strong>進給速度：</strong> {selected_result.get('feed_mm_min', 0)} mm/min</p>
        </div>
    </details>
    """

    # 添加警告訊息
    if 'depth_warning' in selected_result:
        warning_text = selected_result['depth_warning']
        steps_html += warning_text

    return steps_html
