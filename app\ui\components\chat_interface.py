"""
聊天介面元件
負責渲染對話內容和處理用戶輸入
"""

import streamlit as st
import json
import re
import time
from .calculation_panel import toggle_calculation_panel
from ..state_management import clear_input


def render_chat_history(messages, lookup_results, current_conversation_id):
    """渲染對話歷史"""
    # 顯示對話歷史，排除系統提示
    chat_history = [(msg["role"], msg["content"], msg.get("id", None))
                    for msg in messages if msg["role"] != "system"]

    # 創建一個容器來顯示對話
    chat_container = st.container()

    with chat_container:
        for i, (role, content, msg_id) in enumerate(chat_history):
            if role == "user":
                _render_user_message(content)
            else:
                _render_assistant_message(content, msg_id, lookup_results,
                                          current_conversation_id, i)


def _render_user_message(content):
    """渲染用戶訊息"""
    st.markdown(
        f"""
        <div style="display: flex; justify-content: flex-end; margin-bottom: 10px;" class="chat-container">
            <div class="user-message" style="max-width: 80%;">
                <p style="margin: 0;"><strong>你</strong></p>
                <p style="margin: 0;">{content}</p>
            </div>
        </div>
        """,
        unsafe_allow_html=True
    )


def _render_assistant_message(content, msg_id, lookup_results, current_conversation_id, i):
    """渲染助理訊息"""
    # 將Markdown格式轉換為HTML格式
    formatted_content = content.replace('\n', '<br>')  # 換行
    formatted_content = re.sub(
        r'\*\*(.*?)\*\*', r'<strong>\1</strong>', formatted_content)  # 粗體

    st.markdown(
        f"""
        <div style="display: flex; justify-content: flex-start; margin-bottom: 10px;" class="chat-container">
            <div class="assistant-message" style="max-width: 80%;">
                <p style="margin: 0;"><strong>🤖 助理</strong></p>
                <p style="margin: 0; line-height: 1.6;">{formatted_content}</p>
            </div>
        </div>
        """,
        unsafe_allow_html=True
    )

    # 查找與此訊息相關的檢索結果
    related_lookups = [lookup for lookup in lookup_results
                       if lookup["message_id"] == msg_id]

    # 如果有檢索結果，顯示查看數據來源按鈕
    if related_lookups:
        _render_lookup_buttons(related_lookups, current_conversation_id, i)


def _render_lookup_buttons(related_lookups, current_conversation_id, i):
    """渲染檢索結果按鈕"""
    for lookup in related_lookups:
        lookup_data = json.loads(lookup["data"])

        # 顯示查詢參數作為按鈕標籤
        if isinstance(lookup_data, list) and lookup_data:
            params = lookup_data[0].get("query_params", {})
        else:
            params = lookup_data.get("query_params", {})

        if params:
            param_text = (f"材料:{params.get('part_material', '-')} "
                          f"刀具:{params.get('tool_type', '-')} "
                          f"直徑:{params.get('tool_diam', '-')} "
                          f"寬度:{params.get('cut_width_mm', '-')}")
        else:
            param_text = ""

        # 根據面板是否開啟以及是否為當前選中的檢索來決定按鈕文字
        is_current_panel = (st.session_state.show_calculation_panel and
                            st.session_state.selected_lookup_id == lookup["id"])

        # 使用訊息索引和查詢ID生成唯一的 key
        unique_key = f"show_data_{current_conversation_id}_{i}_{lookup['id']}_{int(time.time())}"

        if is_current_panel:
            button_label = f" ✖ 關閉面板 ({param_text})" if param_text else " ✖ 關閉面板"
            # 使用 type="primary" 然後用CSS改成紅色
            st.button(button_label, key=unique_key, type="primary",
                      on_click=toggle_calculation_panel, args=(lookup["id"],))
        else:
            button_label = f"🔬 查看計算詳情 ({param_text})" if param_text else "🔬 查看計算詳情"
            # 使用預設樣式（無背景色）
            st.button(button_label, key=unique_key,
                      on_click=toggle_calculation_panel, args=(lookup["id"],))


def render_user_input(service, db, current_conversation_id):
    """渲染用戶輸入區域並處理提交"""
    with st.form(f"user_input_form_{st.session_state.form_key}"):
        user_input = st.text_input(
            "請輸入查詢內容或補充資料：",
            value=st.session_state.input_text,
            key=f"user_input_field_{st.session_state.form_key}"
        )
        submitted = st.form_submit_button("送出")

        if submitted and user_input.strip():
            _handle_user_input(user_input.strip(), service,
                               db, current_conversation_id)


def _handle_user_input(user_input, service, db, current_conversation_id):
    """處理用戶輸入"""
    st.session_state.turn_count += 1

    if st.session_state.turn_count > st.session_state.max_turns:
        st.error("已達到最大對話輪次，請開始新對話。")
        st.session_state.turn_count = 0
        return

    # 添加用戶消息到數據庫
    db.add_message(current_conversation_id, "user", user_input)

    # 獲取更新後的對話
    messages = db.get_conversation_messages(current_conversation_id)

    # 使用服務生成回覆
    conversation, reply, raw_result = service.chat(messages)

    # 儲存原始檢索結果
    st.session_state.raw_lookup_result = raw_result

    # 添加助理回覆到數據庫
    message_id = db.add_message(current_conversation_id, "assistant", reply)

    # 如果有檢索結果，保存到資料庫
    if raw_result:
        db.add_lookup_result(current_conversation_id, message_id,
                             json.dumps(raw_result, ensure_ascii=False))

    # 更新對話標題（如果是第一次對話）
    _update_conversation_title_if_needed(
        db, current_conversation_id, user_input)

    # 清空輸入框
    clear_input()

    st.rerun()


def _update_conversation_title_if_needed(db, current_conversation_id, user_input):
    """如果是新對話，更新對話標題"""
    conversations = db.get_all_conversations()
    current_conv = next(
        (c for c in conversations if c["id"] == current_conversation_id), None)

    if current_conv and current_conv["title"] == "新對話":
        # 使用用戶的第一個輸入作為對話標題
        title = user_input[:20] + "..." if len(user_input) > 20 else user_input
        db.update_conversation_title(current_conversation_id, title)
