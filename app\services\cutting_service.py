"""
切削參數查詢服務模組
負責核心的切削參數計算和查詢邏輯
"""

from data import lookup_cutting_param
from config import get_config


class CuttingParameterService:
    """純切削參數查詢服務，不包含 UI 相關邏輯"""

    def __init__(self, df):
        self.df = df
        # 載入配置
        config = get_config()
        self.defaults_config = config.defaults_config
        self.safety_limits = self.defaults_config.get('safety_limits', {})

    def query_parameters(self, query_args):
        """
        執行切削參數查詢

        Args:
            query_args: 包含7個參數的字典

        Returns:
            查詢結果字典
        """
        return lookup_cutting_param(self.df, **query_args)

    def format_basic_result(self, args, result, material_name, tool_name):
        """
        格式化基本查詢結果

        Args:
            args: 查詢參數
            result: 查詢結果
            material_name: 材料名稱
            tool_name: 刀具名稱

        Returns:
            格式化的結果字串
        """
        if isinstance(result, dict) and 'error' in result:
            return f"""查詢條件：
- 材料：{material_name} (代碼: {args['part_material']})
- 刀具：{tool_name} (代碼: {args['tool_type']})
- 直徑：{args.get('tool_diam', 'N/A')}mm
- 切寬：{args.get('cut_width_mm', 'N/A')}mm
- 品牌：{args.get('brand', 'N/A')}
- 刃數：{args.get('flute_count', 'N/A')}
- 切深：{args.get('depth_of_cut', 'N/A')}mm

查詢結果：{result['error']}"""

        # 格式化成功結果
        result_text = f"""✅ **查詢完成**

**輸入參數：**
- 材料：{material_name}
- 刀具形式：{tool_name}
- 刀具直徑：{args.get('tool_diam', 'N/A')} mm
- 切寬：{args.get('cut_width_mm', 'N/A')} mm
- 品牌：{args.get('brand', 'N/A')}
- 刃數：{args.get('flute_count', 'N/A')} 刃
- 切深：{args.get('depth_of_cut', 'N/A')} mm

**計算結果：**
- 🔄 **主軸轉速**：{result.get('s_rpm', 'N/A')} rpm
- ➡️ **進給速度**：{result.get('feed_mm_min', 'N/A')} mm/min"""

        # 添加警告（如果有）
        if 'depth_warning' in result:
            result_text += f"\n\n **{result['depth_warning']}**"

        return result_text

    def get_calculation_details(self, result):
        """
        獲取詳細計算過程

        Args:
            result: 查詢結果

        Returns:
            詳細計算過程字串
        """
        if 'calculation_details' not in result:
            return "無計算詳情"

        details = result['calculation_details']
        detail_text = f"""**詳細參數計算：**

**步驟1 - 基礎參數：**
- 標準線速度 Vcstd：{details.get('cutting_speed_std', 0):.1f} m/min
- 切深因子：{details.get('depth_factor', 0):.1f}

**步驟2 - 每刃進刀量計算：**
- Ftstd = X4×D⁴ + X3×D³ + X2×D² + X×D + C
- Ftstd：{details.get('Ftstd', 0):.6f} mm/tooth

**步驟3-4 - 幾何計算：**
- 切寬比 Cw：{details.get('Cw', 0):.3f}
- 建議最大切深 Apstd：{details.get('Apstd', 0):.1f} mm

**步驟5-6 - 角度與修正：**
- 角度 Cdeg：{details.get('Cdeg', 0):.1f}°
- 修正每刃進刀量 Ft：{details.get('Ft', 0):.6f} mm/tooth

**步驟7-9 - 最終計算：**
- 修正線速度 Vc：{details.get('Vc', 0):.1f} m/min
- 主軸轉速計算：N = Vc×1000/(π×D)
- 進給速度計算：F = Ft×N×刃數"""

        return detail_text
