"""
Session State 管理模組
集中管理應用程式的所有狀態變數
"""

import streamlit as st
from config import get_config


def initialize_session_state(db, config=None):
    """初始化所有的 session state 變數"""

    # 載入配置
    if config is None:
        config = get_config()
    app_config = config.app_config

    # 當前對話 ID
    if "current_conversation_id" not in st.session_state:
        conversations = db.get_all_conversations()
        if conversations:
            st.session_state.current_conversation_id = conversations[0]["id"]
        else:
            st.session_state.current_conversation_id = db.create_conversation()

    # 對話標題編輯
    if "new_title" not in st.session_state:
        st.session_state.new_title = ""

    # 對話輪次控制（從配置讀取）
    if "max_turns" not in st.session_state:
        st.session_state.max_turns = app_config.get('max_turns', 5)

    if "turn_count" not in st.session_state:
        st.session_state.turn_count = 0

    # 查詢結果相關
    if "raw_lookup_result" not in st.session_state:
        st.session_state.raw_lookup_result = None

    # 計算面板控制
    if "show_calculation_panel" not in st.session_state:
        st.session_state.show_calculation_panel = False

    if "selected_lookup_id" not in st.session_state:
        st.session_state.selected_lookup_id = None

    # 對話選單控制
    if "show_conversation_menu" not in st.session_state:
        st.session_state.show_conversation_menu = None

    # 輸入框控制
    if "input_text" not in st.session_state:
        st.session_state.input_text = ""

    if "form_key" not in st.session_state:
        st.session_state.form_key = 0


def reset_conversation_state():
    """重置對話相關的狀態"""
    st.session_state.turn_count = 0
    st.session_state.raw_lookup_result = None
    st.session_state.show_calculation_panel = False
    st.session_state.selected_lookup_id = None
    st.session_state.input_text = ""
    st.session_state.form_key += 1


def clear_input():
    """清空輸入框並刷新表單"""
    st.session_state.input_text = ""
    st.session_state.form_key += 1
