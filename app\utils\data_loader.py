"""
資料載入工具模組
負責快取和初始化資料載入
"""

import streamlit as st
from data import load_cutting_params
from database import ChatDatabase
from services import CuttingAssistantService
from config import get_config


@st.cache_resource
def get_database():
    """獲取資料庫實例（快取）"""
    config = get_config()
    db_config = config.database_config
    db_path = db_config.get('path', 'chat_history.db')
    return ChatDatabase(db_path)


@st.cache_resource
def load_cutting_data():
    """載入切削參數資料（快取）"""
    config = get_config()
    data_config = config.data_config
    excel_path = data_config.get(
        'excel_path', 'Data/cut_data.xlsx')

    try:
        df, material_mapping, enhanced_material_mapping, tool_type_mapping, enhanced_tool_mapping = load_cutting_params(
            excel_path)
        return df, material_mapping, enhanced_material_mapping, tool_type_mapping, enhanced_tool_mapping
    except Exception as e:
        st.error(f"無法載入資料：{str(e)}")
        return None, {}, {}, {}, {}


def initialize_service(df, material_mapping, enhanced_material_mapping, tool_type_mapping, enhanced_tool_mapping):
    """初始化切削助理服務"""
    if df is None:
        st.error("系統初始化失敗，請檢查資料檔案。")
        st.stop()
        return None

    # 從配置讀取 LLM 設置
    config = get_config()
    llm_config = config.llm_config

    # 初始化服務（使用配置文件中的設置）
    service = CuttingAssistantService(
        df,
        material_mapping,
        enhanced_material_mapping,
        tool_type_mapping,
        enhanced_tool_mapping
    )

    return service, llm_config.get('base_url')
