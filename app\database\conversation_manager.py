"""
對話管理模組
處理聊天對話的創建、更新、查詢和刪除操作
"""

from datetime import datetime
from .connection import DatabaseConnection


class ConversationManager:
    """對話管理器"""

    def __init__(self, db_connection: DatabaseConnection):
        self.db = db_connection

    def create_conversation(self, title="新對話"):
        """建立新的對話，回傳對話 ID"""
        query = "INSERT INTO conversations (title, created_at, updated_at) VALUES (?, ?, ?)"
        params = (title, datetime.now(), datetime.now())

        conversation_id = self.db.execute_insert(query, params)
        return conversation_id

    def update_conversation_title(self, conversation_id, new_title):
        """更新對話標題"""
        query = "UPDATE conversations SET title = ?, updated_at = ? WHERE id = ?"
        params = (new_title, datetime.now(), conversation_id)

        return self.db.execute_update(query, params)

    def update_conversation_timestamp(self, conversation_id):
        """更新對話的最後更新時間"""
        query = "UPDATE conversations SET updated_at = ? WHERE id = ?"
        params = (datetime.now(), conversation_id)

        return self.db.execute_update(query, params)

    def get_all_conversations(self):
        """取得所有對話的清單，按更新時間排序"""
        query = "SELECT id, title, updated_at FROM conversations ORDER BY updated_at DESC"

        results = self.db.execute_query(query)
        conversations = [
            {
                "id": row[0],
                "title": row[1],
                "updated_at": row[2]
            }
            for row in results
        ]

        return conversations

    def get_conversation_by_id(self, conversation_id):
        """根據 ID 取得特定對話"""
        query = "SELECT id, title, created_at, updated_at FROM conversations WHERE id = ?"
        params = (conversation_id,)

        results = self.db.execute_query(query, params)
        if results:
            row = results[0]
            return {
                "id": row[0],
                "title": row[1],
                "created_at": row[2],
                "updated_at": row[3]
            }
        return None

    def delete_conversation(self, conversation_id):
        """刪除指定的對話及其所有相關資料"""
        # 由於設定了 CASCADE，刪除對話時會自動刪除相關的訊息和檢索結果
        queries = [
            ("DELETE FROM lookup_results WHERE conversation_id = ?", (conversation_id,)),
            ("DELETE FROM messages WHERE conversation_id = ?", (conversation_id,)),
            ("DELETE FROM conversations WHERE id = ?", (conversation_id,))
        ]

        for query, params in queries:
            self.db.execute_update(query, params)

        return True

    def conversation_exists(self, conversation_id):
        """檢查對話是否存在"""
        query = "SELECT COUNT(*) FROM conversations WHERE id = ?"
        params = (conversation_id,)

        results = self.db.execute_query(query, params)
        return results[0][0] > 0 if results else False
