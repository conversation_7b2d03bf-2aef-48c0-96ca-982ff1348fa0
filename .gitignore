############################################################
#  一般 OS／IDE 垃圾檔
############################################################
# macOS
.DS_Store
.AppleDouble
.LSOverride
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
# Linux
.*.swp
.*.swo
# IDE / Editor
.vscode/
.idea/
*.sublime-workspace
*.sublime-project

############################################################
#  Python
############################################################
# 編譯產物
__pycache__/
*.py[cod]
*.pyo
*.pyd
# 打包輪子
*.egg
*.egg-info/
dist/
build/
# 虛擬環境
venv/
.venv/
env/
# pytest / coverage
htmlcov/
.coverage
.cache/
.pytest_cache/
# 日誌
*.log

############################################################
#  Streamlit
############################################################
.streamlit/credentials.toml   # OAuth Token
.streamlit/secrets.toml       # API Key 等機密
# 官方 cache（放 widget snapshots）
.streamlit/static/

############################################################
#  llama.cpp / 模型
############################################################
# 大型 GGUF / GGML / safetensors 檔，建議用 Git LFS 或手動下載
models/
*.gguf
*.ggml
*.safetensors

############################################################
#  打包與執行檔
############################################################
# PyInstaller
*.spec
# 輸出
dist/
build/
# 單檔可執行
*.exe
*.app
*.dmg
*.pkg
*.deb
*.rpm

############################################################
#  Docker
############################################################
*.container
*.dockerfile.swp
docker-compose.override.yml