"""
Excel 資料載入模組
專門處理 Excel 文件的讀取和映射表建立
"""

import pandas as pd


def load_cutting_params(excel_path: str) -> tuple:
    """
    載入切削參數資料並建立增強的映射表
    回傳: (merged_df, material_mapping, enhanced_material_mapping, tool_type_mapping, enhanced_tool_mapping)
    """
    xls = pd.read_excel(excel_path, sheet_name=None)

    # 讀取映射表 (HomePage)
    mapping_df = xls['HomePage']

    # 建立基本材料映射字典
    material_mapping = {}
    tool_type_mapping = {}

    for _, row in mapping_df.iterrows():
        # 跳過有NaN的index行
        if pd.isna(row['index']):
            continue

        index = int(row['index'])
        material = row['材料']
        tool_type = row['刀具型式']

        # 處理材料映射（可能有多個材料名稱用逗號分隔）
        if pd.notna(material):
            materials = [m.strip()
                         for m in str(material).split(',') if m.strip()]
            for mat in materials:
                if mat:  # 確保不是空字串
                    material_mapping[mat] = index

        # 處理刀具型式映射（可能有多個刀具型式用逗號分隔）
        if pd.notna(tool_type):
            tool_types = [t.strip()
                          for t in str(tool_type).split(',') if t.strip()]
            for tool in tool_types:
                if tool:  # 確保不是空字串
                    tool_type_mapping[tool] = index

    # === 建立增強的材料映射（支援簡略名詞） ===
    enhanced_material_mapping = material_mapping.copy()

    # === 建立增強的刀具映射（支援簡略名詞） ===
    enhanced_tool_mapping = tool_type_mapping.copy()

    # 合併所有品牌的資料
    merged_df = _merge_brand_data(xls)

    return merged_df, material_mapping, enhanced_material_mapping, tool_type_mapping, enhanced_tool_mapping


def _merge_brand_data(xls):
    """合併所有品牌的資料"""
    brand_sheets = ['MITSUBISHI MATERIALS', 'ISCAR', 'SANDVIK', 'General']
    dfs = []

    for brand in brand_sheets:
        if brand in xls:
            df = xls[brand].copy()
            df['Brand'] = brand  # 加入品牌欄位
            dfs.append(df)

    # 合併所有資料
    merged_df = pd.concat(dfs, ignore_index=True)

    # 新格式：包含計算因子
    merged_df.rename(columns={
        '材料': 'part_material',
        '刀具型式': 'tool_type',
        '切深因子': 'depth_factor',
        '線速度(Vc)(m/min)': 'cutting_speed',
    }, inplace=True)

    # 型別校正 - 加入錯誤處理
    merged_df = _clean_and_convert_data(merged_df)

    return merged_df


def _clean_and_convert_data(merged_df):
    """清理和轉換資料格式"""
    try:
        # 先移除標題行（如果存在）
        merged_df = merged_df[merged_df['part_material'] != '材料']
        merged_df = merged_df[merged_df['tool_type'] != '刀具型式']

        # 清理數據，移除含有NaN的行
        merged_df = merged_df.dropna(subset=['part_material', 'tool_type'])

        # 轉換為數值型別
        merged_df["part_material"] = pd.to_numeric(
            merged_df["part_material"], errors='coerce')
        merged_df["tool_type"] = pd.to_numeric(
            merged_df["tool_type"], errors='coerce')

        # 移除轉換失敗的行
        merged_df = merged_df.dropna(subset=['part_material', 'tool_type'])

        # 轉換為整數
        merged_df["part_material"] = merged_df["part_material"].astype(int)
        merged_df["tool_type"] = merged_df["tool_type"].astype(int)

        # 處理計算因子欄位
        numeric_cols = ['depth_factor',
                        'cutting_speed', 'X4', 'X3', 'X2', 'X', 'C']
        for col in numeric_cols:
            if col in merged_df.columns:
                merged_df[col] = pd.to_numeric(merged_df[col], errors='coerce')

    except Exception as e:
        print(f"資料轉換警告: {e}")

    return merged_df
