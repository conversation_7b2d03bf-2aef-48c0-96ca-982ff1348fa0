"""
UI 樣式管理模組
包含應用程式的所有 CSS 樣式定義
"""


def get_app_styles():
    """返回應用程式的主要 CSS 樣式"""
    return """
<style>
    /* 基本樣式 */
    .main .block-container {
        padding-top: 2rem;
    }
    
    /* 美化對話氣泡 */
    .user-message {
        background-color: #e6f3ff;
        padding: 12px;
        border-radius: 15px;
        margin-bottom: 15px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    
    .assistant-message {
        background-color: #f0f0f0;
        padding: 12px;
        border-radius: 15px;
        margin-bottom: 15px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    
    /* 改善按鈕樣式 */
    .stButton > button {
        border-radius: 20px;
    }
    
    /* 增加對話容器的間距 */
    .chat-container {
        padding-right: 1.5rem;
    }
    
    /* 真正的浮動面板 - 使用 iframe 技巧 */
    .true-floating-panel {
        position: fixed !important;
        top: 80px !important;
        right: 20px !important;
        width: 450px !important;
        max-height: calc(100vh - 120px) !important;
        background: white !important;
        border: 1px solid #ddd !important;
        border-radius: 10px !important;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        z-index: 9999 !important;
        overflow-y: auto !important;
        display: block !important;
    }
    
    .true-floating-panel .panel-header {
        background: #f8f9fa !important;
        padding: 15px 20px !important;
        border-bottom: 1px solid #eee !important;
        border-radius: 10px 10px 0 0 !important;
    }
    
    .true-floating-panel .panel-content {
        padding: 20px !important;
        max-height: calc(100vh - 200px) !important;
        overflow-y: auto !important;
    }
    
    /* 確保面板正確顯示在最上層 */
    .floating-panel {
        position: fixed !important;
        z-index: 9999 !important;
    }
    
    /* 面板內容樣式 */
    .panel-content {
        padding: 20px;
        max-height: calc(100vh - 200px);
        overflow-y: auto;
    }
    
    /* 確保面板內的 Streamlit 組件正確顯示 */
    .floating-panel .stExpander {
        margin-bottom: 1rem;
    }
    
    .floating-panel .stMarkdown {
        margin-bottom: 0.5rem;
    }
    
    /* 移除全域控制按鈕樣式 */
    
    /* 恢復正常的內容區域間距 */
    .main .block-container {
        padding-right: 20px;
    }
    
    /* 關閉面板按鈕的紅色樣式 */
    div[data-testid="stButton"]:has(button:contains("✖ 關閉面板")) button[kind="primary"] {
        background-color: #ff4444 !important;
        border-color: #ff4444 !important;
        color: white !important;
    }
    
    div[data-testid="stButton"]:has(button:contains("✖ 關閉面板")) button[kind="primary"]:hover {
        background-color: #cc0000 !important;
        border-color: #cc0000 !important;
        color: white !important;
    }
    
    div[data-testid="stButton"]:has(button:contains("✖ 關閉面板")) button[kind="primary"]:focus {
        background-color: #ff4444 !important;
        border-color: #ff4444 !important;
        color: white !important;
        box-shadow: 0 0 0 0.2rem rgba(255, 68, 68, 0.25) !important;
    }
    
    /* 更強的選擇器確保樣式生效 */
    button[kind="primary"]:contains("✖ 關閉面板") {
        background: #ff4444 !important;
        border: 1px solid #ff4444 !important;
        color: white !important;
    }
    
    /* 針對包含 "關閉面板" 文字的按鈕強制紅色 */
    button:contains("✖ 關閉面板") {
        background-color: #ff4444 !important;
        border-color: #ff4444 !important;
        color: white !important;
    }
    
    /* 當前對話的灰色背景樣式 */
    div[data-testid="stButton"]:has(button:contains("●")) button[kind="secondary"] {
        background-color: #f0f2f6 !important;
        border-color: #d6dce5 !important;
        color: #262730 !important;
    }
    
    div[data-testid="stButton"]:has(button:contains("●")) button[kind="secondary"]:hover {
        background-color: #e8ecf0 !important;
        border-color: #c8d0d8 !important;
    }
    
    /* 更強的選擇器確保當前對話樣式生效 */
    button[kind="secondary"]:contains("●") {
        background: #f0f2f6 !important;
        border: 1px solid #d6dce5 !important;
        color: #262730 !important;
    }
</style>
"""
