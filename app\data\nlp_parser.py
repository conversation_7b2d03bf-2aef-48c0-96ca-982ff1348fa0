"""
自然語言解析模組
處理用戶查詢中的切削參數提取
"""

import re
from .smart_matchers import smart_material_matching, smart_tool_matching, smart_brand_matching, determine_brand_by_priority


def parse_natural_language(query: str, material_mapping: dict, enhanced_material_mapping: dict,
                           tool_type_mapping: dict, enhanced_tool_mapping: dict) -> dict:
    """
    7變數自然語言解析，支援7個變數的提取
    """
    result = {
        'part_material': None,
        'tool_type': None,
        'tool_diam': None,
        'cut_width_mm': None,
        'brand': None,
        'flute_count': None,
        'depth_of_cut': None,
        'extracted_info': {},
        'confidence': {
            'part_material': False,
            'tool_type': False,
            'tool_diam': False,
            'cut_width_mm': False,
            'brand': False,
            'flute_count': False,
            'depth_of_cut': False
        }
    }

    # 提取各種參數
    _extract_tool_diameter(query, result)
    _extract_cut_width(query, result)
    _extract_flute_count(query, result)
    _extract_depth_of_cut(query, result)

    # 使用智能材料匹配
    material_code, material_name, material_conf = smart_material_matching(
        query, material_mapping, enhanced_material_mapping)
    if material_code:
        result['part_material'] = material_code
        result['extracted_info']['part_material'] = material_name
        result['confidence']['part_material'] = material_conf in [
            'high', 'medium']

    # 使用智能刀具匹配
    tool_code, tool_name, tool_conf = smart_tool_matching(
        query, tool_type_mapping, enhanced_tool_mapping)
    if tool_code:
        result['tool_type'] = tool_code
        result['extracted_info']['tool_type'] = tool_name
        result['confidence']['tool_type'] = tool_conf in ['high', 'medium']

    # 使用智能品牌匹配
    brand_name, brand_conf = smart_brand_matching(query)
    if brand_name:
        # 確定最終使用的品牌
        final_brand = determine_brand_by_priority(brand_name)
        result['brand'] = final_brand
        result['extracted_info']['brand'] = final_brand
        result['confidence']['brand'] = brand_conf == 'high'

    return result


def _extract_tool_diameter(query: str, result: dict) -> None:
    """提取刀具直徑"""
    diam_patterns = [
        # 支援 "用10mm 4刃端銑刀" 格式
        r'用\s*(\d+)\s*mm\s*(?:\d+刃)?\s*(?:刀具|端銑刀|球刀|圓鼻刀|面銑刀|銑刀)',
        r'[ØΦ直徑]?\s*(\d+)\s*mm\s*(?:的)?(?:刀具|端銑刀|球刀|圓鼻刀|面銑刀|銑刀)',
        r'(\d+)\s*mm\s*(?:的)?(?:刀具|端銑刀|球刀|圓鼻刀|面銑刀|銑刀)',
        r'刀具直徑\s*[：:]?\s*(\d+)\s*mm',
        r'直徑\s*(\d+)\s*mm'
    ]

    for pattern in diam_patterns:
        match = re.search(pattern, query)
        if match:
            result['tool_diam'] = int(match.group(1))
            result['extracted_info']['tool_diam'] = f"{result['tool_diam']}mm"
            result['confidence']['tool_diam'] = True
            break


def _extract_cut_width(query: str, result: dict) -> None:
    """提取切寬"""
    cut_width_patterns = [
        r'切寬\s*[：:]?\s*(\d+(?:\.\d+)?)\s*mm',
        r'切削寬度\s*[：:]?\s*(\d+(?:\.\d+)?)\s*mm',
        r'寬度\s*[：:]?\s*(\d+(?:\.\d+)?)\s*mm',
        r'切\s*(\d+(?:\.\d+)?)\s*mm',  # 新增：支援 "切2mm" 格式
    ]

    for pattern in cut_width_patterns:
        match = re.search(pattern, query)
        if match:
            result['cut_width_mm'] = float(match.group(1))
            result['extracted_info']['cut_width_mm'] = f"{result['cut_width_mm']}mm"
            result['confidence']['cut_width_mm'] = True
            break


def _extract_flute_count(query: str, result: dict) -> None:
    """提取刃數"""
    flute_patterns = [
        r'(\d+)\s*刃',
        r'刃數\s*[：:]?\s*(\d+)',
        r'(\d+)\s*flute',
        r'flute\s*[：:]?\s*(\d+)'
    ]

    for pattern in flute_patterns:
        match = re.search(pattern, query, re.IGNORECASE)
        if match:
            result['flute_count'] = int(match.group(1))
            result['extracted_info']['flute_count'] = f"{result['flute_count']}刃"
            result['confidence']['flute_count'] = True
            break


def _extract_depth_of_cut(query: str, result: dict) -> None:
    """提取切深"""
    depth_patterns = [
        r'切深\s*[：:]?\s*(\d+(?:\.\d+)?)\s*mm',
        r'切削深度\s*[：:]?\s*(\d+(?:\.\d+)?)\s*mm',
        r'深度\s*[：:]?\s*(\d+(?:\.\d+)?)\s*mm',
        r'ap\s*[：:]?\s*(\d+(?:\.\d+)?)\s*mm'
    ]

    for pattern in depth_patterns:
        match = re.search(pattern, query, re.IGNORECASE)
        if match:
            result['depth_of_cut'] = float(match.group(1))
            result['extracted_info']['depth_of_cut'] = f"{result['depth_of_cut']}mm"
            result['confidence']['depth_of_cut'] = True
            break
