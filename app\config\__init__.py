"""
配置管理模組
提供統一的配置載入和存取接口
"""

import yaml
import os
from pathlib import Path
from typing import Dict, Any, Optional


class Config:
    """配置管理類"""

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器

        Args:
            config_path: 配置文件路徑，預設為 app/config.yaml
        """
        if config_path is None:
            # 預設配置文件路徑
            app_root = Path(__file__).parent.parent
            config_path = app_root / "config.yaml"

        self.config_path = Path(config_path)
        self._config: Dict[str, Any] = {}
        self._load_config()

    def _load_config(self):
        """載入配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f) or {}
            print(f"[OK] 配置文件載入成功: {self.config_path}")
        except FileNotFoundError:
            print(f"[WARNING] 配置文件不存在: {self.config_path}")
            self._config = {}
        except yaml.YAMLError as e:
            print(f"[ERROR] 配置文件解析失敗: {e}")
            self._config = {}
        except Exception as e:
            print(f"[ERROR] 載入配置文件失敗: {e}")
            self._config = {}

    def get(self, key: str, default: Any = None) -> Any:
        """
        獲取配置值，支援點號分隔的鍵路徑

        Args:
            key: 配置鍵，支援 'app.title' 格式
            default: 預設值

        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._config

        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default

    def set(self, key: str, value: Any):
        """
        設置配置值

        Args:
            key: 配置鍵
            value: 配置值
        """
        keys = key.split('.')
        config = self._config

        # 建立嵌套字典結構
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]

        config[keys[-1]] = value

    def reload(self):
        """重新載入配置文件"""
        self._load_config()

    def save(self, path: Optional[str] = None):
        """
        保存配置到文件

        Args:
            path: 保存路徑，預設為原配置文件路徑
        """
        save_path = Path(path) if path else self.config_path

        try:
            # 確保目錄存在
            save_path.parent.mkdir(parents=True, exist_ok=True)

            with open(save_path, 'w', encoding='utf-8') as f:
                yaml.dump(self._config, f, default_flow_style=False,
                          allow_unicode=True, indent=2)
            print(f"[OK] 配置文件保存成功: {save_path}")
        except Exception as e:
            print(f"[ERROR] 保存配置文件失敗: {e}")

    def get_section(self, section: str) -> Dict[str, Any]:
        """
        獲取配置區塊

        Args:
            section: 區塊名稱

        Returns:
            配置區塊字典
        """
        return self.get(section, {})

    # === 便利方法 ===

    @property
    def app_config(self) -> Dict[str, Any]:
        """應用程式配置"""
        return self.get_section('app')

    @property
    def llm_config(self) -> Dict[str, Any]:
        """LLM 配置"""
        return self.get_section('llm')

    @property
    def database_config(self) -> Dict[str, Any]:
        """資料庫配置"""
        return self.get_section('database')

    @property
    def data_config(self) -> Dict[str, Any]:
        """資料配置"""
        return self.get_section('data')

    @property
    def defaults_config(self) -> Dict[str, Any]:
        """預設值配置"""
        return self.get_section('defaults')

    @property
    def parameters_config(self) -> Dict[str, Any]:
        """參數配置"""
        return self.get_section('parameters')

    @property
    def prompts_config(self) -> Dict[str, Any]:
        """提示詞配置"""
        return self.get_section('prompts')

    @property
    def logging_config(self) -> Dict[str, Any]:
        """日誌配置"""
        return self.get_section('logging')

    @property
    def cache_config(self) -> Dict[str, Any]:
        """快取配置"""
        return self.get_section('cache')

    @property
    def ui_config(self) -> Dict[str, Any]:
        """UI 配置"""
        return self.get_section('ui')

    @property
    def development_config(self) -> Dict[str, Any]:
        """開發配置"""
        return self.get_section('development')


# 全域配置實例
_config_instance: Optional[Config] = None


def get_config() -> Config:
    """
    獲取全域配置實例

    Returns:
        配置實例
    """
    global _config_instance
    if _config_instance is None:
        _config_instance = Config()
    return _config_instance


def reload_config():
    """重新載入配置"""
    global _config_instance
    if _config_instance:
        _config_instance.reload()


# 便利函數
def get_app_config() -> Dict[str, Any]:
    """獲取應用程式配置"""
    return get_config().app_config


def get_llm_config() -> Dict[str, Any]:
    """獲取 LLM 配置"""
    return get_config().llm_config


def get_database_config() -> Dict[str, Any]:
    """獲取資料庫配置"""
    return get_config().database_config


def get_data_config() -> Dict[str, Any]:
    """獲取資料配置"""
    return get_config().data_config


__all__ = [
    'Config',
    'get_config',
    'reload_config',
    'get_app_config',
    'get_llm_config',
    'get_database_config',
    'get_data_config'
]
