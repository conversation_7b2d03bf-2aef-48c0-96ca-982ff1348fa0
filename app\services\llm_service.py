"""
LLM 服務模組
負責 LLM 客戶端初始化、連線管理和提示詞處理
"""

from openai import OpenAI
from config import get_config
from utils.logger import get_llm_logger
import requests
import json


class LLMService:
    """LLM 服務類，處理所有 LLM 相關功能"""

    def __init__(self, api_key=None, model=None, base_url=None):
        # 初始化日誌器
        self.logger = get_llm_logger()

        # 載入配置
        config = get_config()
        llm_config = config.llm_config
        prompts_config = config.prompts_config

        # 使用配置或參數提供的值
        self.model = model or llm_config.get('model', 'gpt-4o-mini')
        self.base_url = base_url or llm_config.get(
            'base_url', 'http://localhost:8081/v1')
        self.temperature = llm_config.get('temperature', 0.3)
        self.timeout = llm_config.get('timeout', 30)
        self.client = None

        # 初始化 OpenAI 客戶端
        api_key = api_key or llm_config.get('api_key', 'empty')
        self._initialize_client(api_key, self.base_url)

        # 設定系統提示詞（從配置讀取）
        self.sys_prompt = prompts_config.get(
            'system', self._get_default_prompt())

    def _initialize_client(self, api_key, base_url):
        """初始化 LLM 客戶端"""
        try:
            self.client = OpenAI(
                api_key=api_key,
                base_url=base_url
            )
            self.logger.info(f"LLM客戶端已初始化 - 模型: {self.model}, 端點: {base_url}")
        except Exception as e:
            self.logger.warning(f"LLM客戶端初始化失敗: {e}")
            self.client = None

    def _get_default_prompt(self):
        """獲取預設系統提示詞（當配置未提供時使用）"""
        return (
            "你是一位專業的切削參數顧問。請根據用戶查詢和7變數計算結果，提供專業且友善的統整回答。\n\n"
            "**回答要求：**\n"
            "1. 先友善地確認用戶的查詢需求\n"
            "2. 列出結果使用的7個參數\n"
            "3. 重點突出最終計算結果（主軸轉速和進給速度）\n"
            "4. 如有警告訊息，要特別提醒用戶注意\n"
            "5. 不要重複用戶輸入的原始查詢內容\n\n"
            "**回答風格：**\n"
            "- 專業但親切，像資深技師在指導\n"
            "- 結構清晰，重點明確\n"
            "- 如果有安全注意事項要特別強調\n"
        )

    def is_available(self):
        """檢查 LLM 服務是否可用"""
        return self.client is not None

    def test_connection(self):
        """測試實際的連接是否可用"""
        if not self.is_available():
            return False, "客戶端未初始化"

        try:
            # 嘗試發送一個簡單的測試請求，限制最大 token 數量和超時時間
            response = self.client.chat.completions.create(
                model=self.model,
                temperature=0.1,
                max_tokens=5,  # 只需要很少的回應
                timeout=5,     # 5秒超時
                messages=[
                    {"role": "user", "content": "hi"}  # 簡單的測試訊息
                ]
            )
            return True, "連接成功"
        except Exception as e:
            error_msg = str(e)
            self.logger.warning(f"LLM連接測試失敗: {error_msg}")

            # 根據錯誤類型提供更友好的錯誤訊息
            if "Connection" in error_msg or "connect" in error_msg.lower():
                return False, "無法連接到 LLM 伺服器"
            elif "timeout" in error_msg.lower():
                return False, "連接超時"
            elif "404" in error_msg:
                return False, "模型不存在或路徑錯誤"
            else:
                return False, f"連接錯誤: {error_msg[:30]}..."

    def get_connection_status(self):
        """獲取連接狀態信息"""
        if not self.is_available():
            return {"available": False, "message": "客戶端未初始化"}

        is_connected, message = self.test_connection()
        return {
            "available": is_connected,
            "message": message,
            "model": self.model,
            "base_url": self.base_url
        }

    def generate_response(self, user_query: str, formatted_result: str, temperature=None):
        """生成 LLM 回應"""
        if not self.is_available():
            return None, "LLM 服務不可用"

        try:
            # 使用配置中的溫度值或參數提供的值
            temp = temperature if temperature is not None else self.temperature

            # 將系統提示詞合併到用戶訊息中，避免使用 system role
            combined_prompt = f"{self.sys_prompt}\n\n用戶查詢：{user_query}\n\n{formatted_result}"

            response = self.client.chat.completions.create(
                model=self.model,
                temperature=temp,
                messages=[
                    {"role": "user", "content": combined_prompt}
                ]
            )

            return response.choices[0].message.content, None

        except Exception as e:
            error_msg = f"LLM調用失敗: {str(e)}"
            self.logger.error(error_msg)
            return None, error_msg

    def get_fallback_response(self, formatted_result: str, error_msg: str):
        """獲取 LLM 失敗時的後備回應"""
        return f"""{formatted_result}

*註：LLM統整功能暫時無法使用（{error_msg[:50]}...），已為您提供原始計算結果。*"""
