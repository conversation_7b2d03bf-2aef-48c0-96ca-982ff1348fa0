"""
訊息格式化服務模組
負責各種類型回應訊息的格式化
"""

from config import get_config


class MessageFormatterService:
    """訊息格式化服務類"""

    def __init__(self):
        # 載入配置中的提示詞
        config = get_config()
        self.prompts_config = config.prompts_config

    def format_missing_params_message(self, missing_params: list) -> str:
        """
        格式化7變數模式的缺失參數提示訊息，按類別分組顯示

        Args:
            missing_params: 缺失的參數列表

        Returns:
            格式化的提示訊息
        """
        # 材料資訊
        material_info = []
        if 'part_material' in missing_params:
            material_info.append(
                "  • **材料類型**（例如：鋁合金6061、不鏽鋼304、中碳鋼S45C、鑄鐵F30等）")

        # 刀具規格
        tool_info = []
        if 'tool_type' in missing_params:
            tool_info.append("  • **刀具形式**（例如：端銑刀、球刀、圓鼻刀等）")
        if 'tool_diam' in missing_params:
            tool_info.append("  • **刀具直徑**（例如：10mm, 8mm）")
        if 'brand' in missing_params:
            tool_info.append(
                "  • **品牌**（MITSUBISHI MATERIALS、ISCAR、SANDVIK 或其他）")
        if 'flute_count' in missing_params:
            tool_info.append("  • **刃數**（例如：2刃、4刃）")

        # 切削條件
        cutting_info = []
        if 'cut_width_mm' in missing_params:
            cutting_info.append("  • **切寬**（例如：2mm, 1.5mm）")
        if 'depth_of_cut' in missing_params:
            cutting_info.append("  • **切深**（例如：1mm, 0.5mm）")

        # 組建完整訊息
        result_lines = []
        result_lines.append("為了提供準確的切削參數建議，請提供以下缺失的資訊：")
        result_lines.append("")

        if material_info:
            result_lines.append("📋 **材料資訊**")
            for item in material_info:
                result_lines.append(item)
            result_lines.append("")

        if tool_info:
            result_lines.append("🔧 **刀具規格**")
            for item in tool_info:
                result_lines.append(item)
            result_lines.append("")

        if cutting_info:
            result_lines.append("⚙️ **切削條件**")
            for item in cutting_info:
                result_lines.append(item)
            result_lines.append("")

        result_lines.append("💡 **小提示**：我支援簡略名詞，例如「鋁合金」、「不鏽鋼」、「端銑」、「三菱」等！")
        result_lines.append("")
        result_lines.append("✨ **系統功能**：需要提供完整的7個參數來獲得最精確的切削參數建議。")

        return "\n".join(result_lines)

    def format_welcome_message(self):
        """格式化歡迎訊息"""
        return self.prompts_config.get('welcome',
                                       "您好！我是切削參數查詢助理（7變數模式）。"
                                       "請提供您的切削需求，包含完整的7個參數：材料、刀具形式、刀具直徑、切寬、品牌、刃數、切深。")

    def format_request_query_message(self):
        """格式化請求查詢訊息"""
        return self.prompts_config.get('request_query', "請提供您的查詢內容。")
