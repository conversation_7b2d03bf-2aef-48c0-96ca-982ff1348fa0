"""
聊天資料庫主要接口
整合所有資料庫管理器，提供統一的API接口
"""

from .connection import DatabaseConnection
from .conversation_manager import ConversationManager
from .message_manager import MessageManager
from .lookup_manager import LookupManager


class ChatDatabase:
    """聊天資料庫主要類，整合所有資料庫操作"""

    def __init__(self, db_path="chat_history.db"):
        # 初始化資料庫連接
        self.db_connection = DatabaseConnection(db_path)

        # 初始化各種管理器
        self.conversations = ConversationManager(self.db_connection)
        self.messages = MessageManager(self.db_connection)
        self.lookups = LookupManager(self.db_connection)

        # 為了向後兼容，保留原有的屬性
        self.db_path = db_path

    # === 向後兼容的方法 ===

    def init_db(self):
        """初始化資料庫 - 向後兼容"""
        self.db_connection.init_db()

    def create_conversation(self, title="新對話"):
        """建立新的對話，回傳對話 ID - 向後兼容"""
        return self.conversations.create_conversation(title)

    def update_conversation_title(self, conversation_id, new_title):
        """更新對話標題 - 向後兼容"""
        return self.conversations.update_conversation_title(conversation_id, new_title)

    def add_message(self, conversation_id, role, content):
        """新增訊息到指定對話，返回訊息 ID - 向後兼容"""
        message_id = self.messages.add_message(conversation_id, role, content)
        # 更新對話時間戳
        self.conversations.update_conversation_timestamp(conversation_id)
        return message_id

    def add_lookup_result(self, conversation_id, message_id, data):
        """儲存檢索結果，返回檢索結果 ID - 向後兼容"""
        return self.lookups.add_lookup_result(conversation_id, message_id, data)

    def get_conversation_messages(self, conversation_id):
        """取得指定對話的所有訊息 - 向後兼容"""
        messages = self.messages.get_conversation_messages(conversation_id)
        # 為了向後兼容，保持原有的格式
        return [{"id": msg["id"], "role": msg["role"], "content": msg["content"]} for msg in messages]

    def get_conversation_lookups(self, conversation_id):
        """取得指定對話的所有檢索結果 - 向後兼容"""
        lookups = self.lookups.get_conversation_lookups(conversation_id)
        # 為了向後兼容，保持原有的格式
        return [{"id": lookup["id"], "message_id": lookup["message_id"], "data": lookup["data"]} for lookup in lookups]

    def get_lookup_by_id(self, lookup_id):
        """根據 ID 取得特定的檢索結果 - 向後兼容"""
        lookup = self.lookups.get_lookup_by_id(lookup_id)
        if lookup:
            return {
                "id": lookup["id"],
                "conversation_id": lookup["conversation_id"],
                "message_id": lookup["message_id"],
                "data": lookup["data"]
            }
        return None

    def get_all_conversations(self):
        """取得所有對話的清單，按更新時間排序 - 向後兼容"""
        return self.conversations.get_all_conversations()

    def delete_conversation(self, conversation_id):
        """刪除指定的對話及其所有訊息 - 向後兼容"""
        return self.conversations.delete_conversation(conversation_id)

    # === 新增的便利方法 ===

    def get_conversation_summary(self, conversation_id):
        """取得對話摘要信息"""
        conversation = self.conversations.get_conversation_by_id(
            conversation_id)
        if not conversation:
            return None

        messages = self.messages.get_conversation_messages(conversation_id)
        lookups = self.lookups.get_conversation_lookups(conversation_id)

        return {
            "conversation": conversation,
            "message_count": len(messages),
            "lookup_count": len(lookups),
            "latest_message": messages[-1] if messages else None
        }

    def get_message_with_lookups(self, message_id):
        """取得訊息及其相關的檢索結果"""
        message = self.messages.get_message_by_id(message_id)
        if not message:
            return None

        lookups = self.lookups.get_message_lookups(message_id)

        return {
            "message": message,
            "lookups": lookups
        }

    def cleanup_conversation(self, conversation_id):
        """清理對話相關的所有資料"""
        return self.conversations.delete_conversation(conversation_id)
